// API request and response DTOs

import { Profile, Workout, Group, Session } from './database';

// Common API response wrapper
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

// Pagination
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error: string | null;
  success: boolean;
}

// Profile API DTOs
export interface CreateProfileRequest {
  profile_name?: string;
  height_cm?: number;
  date_of_birth?: string;
}

export interface UpdateProfileRequest {
  profile_name?: string;
  height_cm?: number;
  date_of_birth?: string;
}

export type ProfileResponse = Profile;

// Workout API DTOs
export interface CreateWorkoutRequest {
  name: string;
  description?: string;
  muscle_group?: string;
  equipment?: string;
  image_url?: string;
  video_url?: string;
  is_public?: boolean;
}

export interface UpdateWorkoutRequest {
  name?: string;
  description?: string;
  muscle_group?: string;
  equipment?: string;
  image_url?: string;
  video_url?: string;
  is_public?: boolean;
}

export type WorkoutResponse = Workout;

export interface WorkoutWithSessionsResponse extends Workout {
  sessions: Session[];
  totalSessions: number;
  lastSession?: Session;
}

// Group API DTOs
export interface CreateGroupRequest {
  name: string;
  description?: string;
  workout_ids?: string[]; // Optional array of workout IDs to add to group
}

export interface UpdateGroupRequest {
  name?: string;
  description?: string;
}

export interface AddWorkoutToGroupRequest {
  workout_id: string;
  order_index?: number;
}

export type GroupResponse = Group;

export interface GroupWithWorkoutsResponse extends Group {
  workouts: (Workout & { order_index: number })[];
  totalWorkouts: number;
}

// Session API DTOs
export interface CreateSessionRequest {
  workout_id: string;
  set_count?: number;
  rep_count?: number;
  weight_kg?: number;
  time_seconds?: number;
  notes?: string;
  session_date?: string;
}

export interface UpdateSessionRequest {
  set_count?: number;
  rep_count?: number;
  weight_kg?: number;
  time_seconds?: number;
  notes?: string;
  session_date?: string;
}

export type SessionResponse = Session;

export interface SessionWithWorkoutResponse extends Session {
  workout: Workout;
}

// Search and Filter DTOs
export interface WorkoutSearchParams extends PaginationParams {
  search?: string;
  muscle_group?: string;
  equipment?: string;
  is_public?: boolean;
  user_id?: string;
}

export interface SessionFilterParams extends PaginationParams {
  workout_id?: string;
  date_from?: string;
  date_to?: string;
  user_id?: string;
}

// Statistics DTOs
export interface WorkoutStatsResponse {
  workout_id: string;
  workout_name: string;
  total_sessions: number;
  total_sets: number;
  total_reps: number;
  total_weight: number;
  total_time: number;
  average_weight: number;
  max_weight: number;
  last_session_date: string | null;
  first_session_date: string | null;
}

export interface UserStatsResponse {
  total_workouts: number;
  total_sessions: number;
  total_workout_time: number;
  favorite_muscle_group: string | null;
  most_used_equipment: string | null;
  current_streak: number;
  longest_streak: number;
  workouts_this_week: number;
  workouts_this_month: number;
}

// Error DTOs
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: ValidationError[];
}
