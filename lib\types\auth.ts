// Authentication and user-related types

import { User } from '@supabase/supabase-js';
import { Profile } from './database';

// Extended user type that includes profile data
export interface UserWithProfile extends User {
  profile?: Profile;
}

// Authentication form types
export interface LoginFormData {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface SignUpFormData {
  email: string;
  password: string;
  confirm_password: string;
  profile_name?: string;
  terms_accepted: boolean;
}

export interface ForgotPasswordFormData {
  email: string;
}

export interface ResetPasswordFormData {
  password: string;
  confirm_password: string;
}

export interface UpdatePasswordFormData {
  current_password: string;
  new_password: string;
  confirm_new_password: string;
}

// Authentication state
export interface AuthState {
  user: UserWithProfile | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
}

// Session management
export interface SessionInfo {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: UserWithProfile;
}

// User preferences
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  units: {
    weight: 'kg' | 'lbs';
    distance: 'km' | 'miles';
    temperature: 'celsius' | 'fahrenheit';
  };
  notifications: {
    email_enabled: boolean;
    push_enabled: boolean;
    workout_reminders: boolean;
    progress_updates: boolean;
    social_interactions: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'friends' | 'private';
    workout_visibility: 'public' | 'friends' | 'private';
    allow_friend_requests: boolean;
  };
}

// OAuth provider types
export type OAuthProvider = 'google' | 'github' | 'apple' | 'facebook';

export interface OAuthSignInOptions {
  provider: OAuthProvider;
  redirectTo?: string;
  scopes?: string;
}

// Account management
export interface AccountSettings {
  email: string;
  email_verified: boolean;
  phone?: string;
  phone_verified: boolean;
  two_factor_enabled: boolean;
  last_sign_in: string;
  created_at: string;
}

// Profile completion
export interface ProfileCompletionStatus {
  basic_info: boolean; // name, email
  physical_info: boolean; // height, date of birth
  preferences: boolean; // units, notifications
  first_workout: boolean;
  completion_percentage: number;
}

// User roles and permissions
export enum UserRole {
  USER = 'user',
  PREMIUM = 'premium',
  TRAINER = 'trainer',
  ADMIN = 'admin',
}

export interface UserPermissions {
  can_create_public_workouts: boolean;
  can_access_premium_content: boolean;
  can_moderate_content: boolean;
  can_access_analytics: boolean;
  max_workouts: number | null;
  max_groups: number | null;
}

// Social features
export interface UserConnection {
  id: string;
  user_id: string;
  connected_user_id: string;
  status: 'pending' | 'accepted' | 'blocked';
  created_at: string;
  updated_at: string;
}

export interface FriendRequest {
  id: string;
  from_user_id: string;
  to_user_id: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined';
  created_at: string;
}

// Subscription and billing
export interface Subscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  is_popular: boolean;
  trial_days: number;
}

// Error types
export interface AuthError {
  message: string;
  code?: string;
  field?: string;
}

// API response types for auth endpoints
export interface AuthResponse {
  user: UserWithProfile | null;
  session: SessionInfo | null;
  error: AuthError | null;
}

export interface ProfileUpdateResponse {
  profile: Profile;
  error: AuthError | null;
}
