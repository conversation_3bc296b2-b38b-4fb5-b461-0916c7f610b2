// Workout domain-specific types and enums

// Enums for common workout categories
export enum MuscleGroup {
  CHEST = 'Chest',
  BACK = 'Back',
  SHOULDERS = 'Shoulders',
  ARMS = 'Arms',
  BICEPS = 'Biceps',
  TRICEPS = 'Triceps',
  LEGS = 'Legs',
  QUADRICEPS = 'Quadriceps',
  HAMSTRINGS = 'Hamstrings',
  CALVES = 'Calves',
  GLUTES = 'Glutes',
  CORE = 'Core',
  ABS = 'Abs',
  CARDIO = 'Cardio',
  FULL_BODY = 'Full Body',
}

export enum Equipment {
  NONE = 'None',
  DUMBBELLS = 'Dumbbells',
  BARBELL = 'Barbell',
  KETTLEBELL = 'Kettlebell',
  RESISTANCE_BANDS = 'Resistance Bands',
  PULL_UP_BAR = 'Pull-up Bar',
  BENCH = 'Bench',
  CABLE_MACHINE = 'Cable Machine',
  SMITH_MACHINE = 'Smith Machine',
  TREADMILL = 'Treadmill',
  STATIONARY_BIKE = 'Stationary Bike',
  ROWING_MACHINE = 'Rowing Machine',
  MEDICINE_BALL = 'Medicine Ball',
  FOAM_ROLLER = 'Foam Roller',
  YOGA_MAT = 'Yoga Mat',
  OTHER = 'Other',
}

export enum WorkoutDifficulty {
  BEGINNER = 'Beginner',
  INTERMEDIATE = 'Intermediate',
  ADVANCED = 'Advanced',
  EXPERT = 'Expert',
}

export enum SessionType {
  STRENGTH = 'Strength',
  CARDIO = 'Cardio',
  FLEXIBILITY = 'Flexibility',
  ENDURANCE = 'Endurance',
  HIIT = 'HIIT',
  RECOVERY = 'Recovery',
}

// Workout template types
export interface WorkoutTemplate {
  id: string;
  name: string;
  description?: string;
  muscle_groups: MuscleGroup[];
  equipment: Equipment[];
  difficulty: WorkoutDifficulty;
  estimated_duration_minutes: number;
  instructions: string[];
  tips?: string[];
  image_url?: string;
  video_url?: string;
  is_premium: boolean;
}

// Exercise set types
export interface ExerciseSet {
  set_number: number;
  reps?: number;
  weight_kg?: number;
  duration_seconds?: number;
  distance_meters?: number;
  rest_seconds?: number;
  notes?: string;
  completed: boolean;
}

// Workout plan types
export interface WorkoutPlan {
  id: string;
  name: string;
  description?: string;
  user_id: string;
  duration_weeks: number;
  difficulty: WorkoutDifficulty;
  goals: string[];
  schedule: WorkoutSchedule[];
  created_at: string;
  updated_at: string;
}

export interface WorkoutSchedule {
  day_of_week: number; // 0-6 (Sunday-Saturday)
  workout_ids: string[];
  rest_day: boolean;
  notes?: string;
}

// Progress tracking types
export interface ProgressEntry {
  id: string;
  user_id: string;
  workout_id: string;
  date: string;
  personal_record: boolean;
  improvement_percentage?: number;
  notes?: string;
  measurements?: BodyMeasurement[];
}

export interface BodyMeasurement {
  type: MeasurementType;
  value: number;
  unit: string;
  date: string;
}

export enum MeasurementType {
  WEIGHT = 'weight',
  BODY_FAT = 'body_fat',
  MUSCLE_MASS = 'muscle_mass',
  CHEST = 'chest',
  WAIST = 'waist',
  HIPS = 'hips',
  BICEP = 'bicep',
  THIGH = 'thigh',
  NECK = 'neck',
}

// Workout session state
export interface ActiveWorkoutSession {
  id: string;
  workout_id: string;
  user_id: string;
  started_at: string;
  current_exercise_index: number;
  completed_sets: ExerciseSet[];
  is_paused: boolean;
  total_rest_time: number;
  notes?: string;
}

// Workout analytics
export interface WorkoutAnalytics {
  workout_id: string;
  period: AnalyticsPeriod;
  total_sessions: number;
  average_duration: number;
  total_volume: number; // Total weight lifted
  progress_trend: 'improving' | 'declining' | 'stable';
  consistency_score: number; // 0-100
  personal_records: number;
}

export enum AnalyticsPeriod {
  WEEK = 'week',
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year',
  ALL_TIME = 'all_time',
}

// Form validation types
export interface WorkoutFormData {
  name: string;
  description?: string;
  muscle_group?: MuscleGroup;
  equipment?: Equipment;
  difficulty?: WorkoutDifficulty;
  estimated_duration?: number;
  is_public?: boolean;
  image_url?: string;
  video_url?: string;
}

export interface SessionFormData {
  workout_id: string;
  sets: ExerciseSet[];
  session_date?: string;
  notes?: string;
  session_type?: SessionType;
}

// UI state types
export interface WorkoutListFilters {
  muscle_groups: MuscleGroup[];
  equipment: Equipment[];
  difficulty: WorkoutDifficulty[];
  is_public?: boolean;
  search_term?: string;
  sort_by: 'name' | 'created_at' | 'popularity' | 'difficulty';
  sort_order: 'asc' | 'desc';
}

export interface SessionListFilters {
  date_range: {
    start: string;
    end: string;
  };
  workout_ids: string[];
  session_types: SessionType[];
  sort_by: 'date' | 'duration' | 'volume';
  sort_order: 'asc' | 'desc';
}
