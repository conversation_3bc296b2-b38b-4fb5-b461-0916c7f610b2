"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import type { OnboardingFormData, ApiResponse, Profile } from "@/lib/types";

interface OnboardingFormProps extends React.ComponentProps<"div"> {}

export function OnboardingForm({ className, ...props }: OnboardingFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<OnboardingFormData>({
    profile_name: "",
    height_cm: null,
    date_of_birth: null,
  });

  const handleInputChange = (field: keyof OnboardingFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'height_cm' ? (value ? parseFloat(value) : null) : value || null
    }));
  };

  const validateForm = (): string[] => {
    const errors: string[] = [];

    if (!formData.profile_name.trim()) {
      errors.push("Profile name is required");
    }

    if (formData.height_cm !== null && formData.height_cm !== undefined) {
      if (formData.height_cm <= 0 || formData.height_cm > 300) {
        errors.push("Height must be between 1 and 300 cm");
      }
    }

    if (formData.date_of_birth) {
      const birthDate = new Date(formData.date_of_birth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (birthDate > today) {
        errors.push("Date of birth cannot be in the future");
      } else if (age > 120) {
        errors.push("Please enter a valid date of birth");
      }
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // Validate form
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      setError(validationErrors[0]);
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/profile/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result: ApiResponse<Profile> = await response.json();

      if (!result.success || result.error) {
        throw new Error(result.error || 'Failed to create profile');
      }

      // Success! Redirect to success page
      router.push('/onboard/success');
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Complete Your Profile</CardTitle>
          <CardDescription>
            Tell us a bit about yourself to get started with your fitness journey
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              {/* Profile Name - Required */}
              <div className="grid gap-2">
                <Label htmlFor="profile_name">
                  Profile Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="profile_name"
                  type="text"
                  placeholder="Enter your name"
                  required
                  value={formData.profile_name}
                  onChange={(e) => handleInputChange('profile_name', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  This is how you'll appear to other users
                </p>
              </div>

              {/* Height - Optional */}
              <div className="grid gap-2">
                <Label htmlFor="height_cm">Height (cm)</Label>
                <Input
                  id="height_cm"
                  type="number"
                  placeholder="170"
                  min="1"
                  max="300"
                  value={formData.height_cm || ""}
                  onChange={(e) => handleInputChange('height_cm', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Optional - helps with fitness calculations
                </p>
              </div>

              {/* Date of Birth - Optional */}
              <div className="grid gap-2">
                <Label htmlFor="date_of_birth">Date of Birth</Label>
                <Input
                  id="date_of_birth"
                  type="date"
                  value={formData.date_of_birth || ""}
                  onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  Optional - helps personalize your experience
                </p>
              </div>

              {error && (
                <div className="p-3 text-sm text-red-500 bg-red-50 border border-red-200 rounded-md">
                  {error}
                </div>
              )}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Setting up your profile..." : "Complete Setup"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
