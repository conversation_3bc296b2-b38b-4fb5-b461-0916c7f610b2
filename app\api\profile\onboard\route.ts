import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import type { 
  OnboardingFormData, 
  ApiResponse, 
  Profile, 
  ProfileInsert,
  SupabaseResponse 
} from '@/lib/types';
import { toApiResponse } from '@/lib/types';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse request body
    const body: OnboardingFormData = await request.json();
    
    // Validate required fields
    if (!body.profile_name?.trim()) {
      const errorResponse: ApiResponse<Profile> = {
        data: null,
        error: 'Profile name is required',
        success: false
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // Get authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      const errorResponse: ApiResponse<Profile> = {
        data: null,
        error: 'Authentication required',
        success: false
      };
      return NextResponse.json(errorResponse, { status: 401 });
    }

    // Check if profile already exists
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    if (existingProfile) {
      const errorResponse: ApiResponse<Profile> = {
        data: null,
        error: 'Profile already exists',
        success: false
      };
      return NextResponse.json(errorResponse, { status: 409 });
    }

    // Prepare profile data
    const profileData: ProfileInsert = {
      id: user.id,
      profile_name: body.profile_name.trim(),
      email: user.email || null,
      height_cm: body.height_cm || null,
      date_of_birth: body.date_of_birth || null,
    };

    // Insert profile into database
    const supabaseResponse: SupabaseResponse<Profile> = await supabase
      .from('profiles')
      .insert(profileData)
      .select()
      .single();

    // Convert Supabase response to API response
    const apiResponse = toApiResponse(supabaseResponse);

    if (!apiResponse.success) {
      return NextResponse.json(apiResponse, { status: 500 });
    }

    return NextResponse.json(apiResponse, { status: 201 });

  } catch (error) {
    console.error('Onboarding error:', error);
    
    const errorResponse: ApiResponse<Profile> = {
      data: null,
      error: error instanceof Error ? error.message : 'Internal server error',
      success: false
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

// Optional: GET method to check if user has completed onboarding
export async function GET(): Promise<NextResponse> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      const errorResponse: ApiResponse<Profile> = {
        data: null,
        error: 'Authentication required',
        success: false
      };
      return NextResponse.json(errorResponse, { status: 401 });
    }

    // Check if profile exists
    const supabaseResponse: SupabaseResponse<Profile> = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    const apiResponse = toApiResponse(supabaseResponse);
    
    return NextResponse.json(apiResponse);

  } catch (error) {
    console.error('Profile check error:', error);
    
    const errorResponse: ApiResponse<Profile> = {
      data: null,
      error: error instanceof Error ? error.message : 'Internal server error',
      success: false
    };
    
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
