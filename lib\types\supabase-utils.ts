// Utility functions for converting between Supabase responses and API responses

import { 
  SupabaseResponse, 
  SupabaseListResponse, 
  PostgrestError 
} from './database';
import { 
  ApiResponse, 
  PaginatedResponse 
} from './api';

/**
 * Converts a Supabase response to an API response
 */
export function toApiResponse<T>(supabaseResponse: SupabaseResponse<T>): ApiResponse<T> {
  const { data, error } = supabaseResponse;
  
  return {
    data,
    error: error ? formatSupabaseError(error) : null,
    success: !error
  };
}

/**
 * Converts a Supabase list response to a paginated API response
 */
export function toPaginatedResponse<T>(
  supabaseResponse: SupabaseListResponse<T>,
  pagination: {
    page: number;
    limit: number;
    total?: number;
  }
): PaginatedResponse<T> {
  const { data, error, count } = supabaseResponse;
  const total = count ?? pagination.total ?? 0;
  const totalPages = Math.ceil(total / pagination.limit);
  
  return {
    data: data ?? [],
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1
    },
    error: error ? formatSupabaseError(error) : null,
    success: !error
  };
}

/**
 * Formats a Supabase PostgrestError into a user-friendly string
 */
export function formatSupabaseError(error: PostgrestError): string {
  // Handle common Supabase error codes
  switch (error.code) {
    case 'PGRST116':
      return 'No data found';
    case 'PGRST301':
      return 'Duplicate entry';
    case '23505':
      return 'This item already exists';
    case '23503':
      return 'Referenced item does not exist';
    case '42501':
      return 'Permission denied';
    case 'PGRST302':
      return 'Multiple records found when expecting one';
    default:
      // Return the message, or a generic error if message is too technical
      if (error.message.includes('duplicate key value')) {
        return 'This item already exists';
      }
      if (error.message.includes('violates foreign key constraint')) {
        return 'Referenced item does not exist';
      }
      if (error.message.includes('permission denied')) {
        return 'You do not have permission to perform this action';
      }
      
      return error.message || 'An unexpected error occurred';
  }
}

/**
 * Type guard to check if a response has an error
 */
export function hasSupabaseError<T>(response: SupabaseResponse<T>): response is SupabaseResponse<T> & { error: PostgrestError } {
  return response.error !== null;
}

/**
 * Type guard to check if a response has data
 */
export function hasSupabaseData<T>(response: SupabaseResponse<T>): response is SupabaseResponse<T> & { data: T } {
  return response.data !== null && response.error === null;
}

/**
 * Utility to handle Supabase response with proper error handling
 */
export function handleSupabaseResponse<T>(
  response: SupabaseResponse<T>,
  errorMessage?: string
): T {
  if (hasSupabaseError(response)) {
    throw new Error(errorMessage || formatSupabaseError(response.error));
  }
  
  if (!hasSupabaseData(response)) {
    throw new Error(errorMessage || 'No data returned');
  }
  
  return response.data;
}

/**
 * Utility for safe Supabase operations that return ApiResponse
 */
export async function safeSupabaseOperation<T>(
  operation: () => Promise<SupabaseResponse<T>>,
  errorMessage?: string
): Promise<ApiResponse<T>> {
  try {
    const response = await operation();
    return toApiResponse(response);
  } catch (error) {
    return {
      data: null,
      error: errorMessage || (error instanceof Error ? error.message : 'Unknown error'),
      success: false
    };
  }
}

/**
 * Utility for safe Supabase list operations that return PaginatedResponse
 */
export async function safeSupabaseListOperation<T>(
  operation: () => Promise<SupabaseListResponse<T>>,
  pagination: { page: number; limit: number; total?: number },
  errorMessage?: string
): Promise<PaginatedResponse<T>> {
  try {
    const response = await operation();
    return toPaginatedResponse(response, pagination);
  } catch (error) {
    return {
      data: [],
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      error: errorMessage || (error instanceof Error ? error.message : 'Unknown error'),
      success: false
    };
  }
}

/**
 * Creates a standardized error response
 */
export function createErrorResponse<T>(message: string): ApiResponse<T> {
  return {
    data: null,
    error: message,
    success: false
  };
}

/**
 * Creates a standardized success response
 */
export function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    data,
    error: null,
    success: true
  };
}

/**
 * Utility to extract data from Supabase response or throw error
 */
export function unwrapSupabaseResponse<T>(
  response: SupabaseResponse<T>,
  errorMessage?: string
): T | null {
  if (response.error) {
    console.error('Supabase error:', response.error);
    throw new Error(errorMessage || formatSupabaseError(response.error));
  }
  return response.data;
}

/**
 * Utility to extract data array from Supabase list response or throw error
 */
export function unwrapSupabaseListResponse<T>(
  response: SupabaseListResponse<T>,
  errorMessage?: string
): T[] {
  if (response.error) {
    console.error('Supabase error:', response.error);
    throw new Error(errorMessage || formatSupabaseError(response.error));
  }
  return response.data ?? [];
}
