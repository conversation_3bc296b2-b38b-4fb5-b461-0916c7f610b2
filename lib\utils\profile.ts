// Profile utility functions

import { createClient } from '@/lib/supabase/server';
import type { Profile, SupabaseResponse } from '@/lib/types';

/**
 * Check if the current user has completed onboarding
 */
export async function hasCompletedOnboarding(): Promise<boolean> {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return false;

    const { data: profile }: SupabaseResponse<Profile> = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .single();

    return !!profile;
  } catch (error) {
    console.error('Error checking onboarding status:', error);
    return false;
  }
}

/**
 * Get the current user's profile
 */
export async function getCurrentUserProfile(): Promise<Profile | null> {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    const { data: profile }: SupabaseResponse<Profile> = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    return profile;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

/**
 * Calculate profile completion percentage
 */
export function calculateProfileCompletion(profile: Profile): number {
  let completedFields = 0;
  const totalFields = 4; // profile_name, email, height_cm, date_of_birth

  if (profile.profile_name) completedFields++;
  if (profile.email) completedFields++;
  if (profile.height_cm) completedFields++;
  if (profile.date_of_birth) completedFields++;

  return Math.round((completedFields / totalFields) * 100);
}

/**
 * Check if profile has required fields completed
 */
export function hasRequiredProfileFields(profile: Profile): boolean {
  return !!profile.profile_name;
}

/**
 * Format profile data for display
 */
export function formatProfileForDisplay(profile: Profile) {
  return {
    name: profile.profile_name || 'Anonymous User',
    email: profile.email || 'No email provided',
    height: profile.height_cm ? `${profile.height_cm} cm` : 'Not specified',
    dateOfBirth: profile.date_of_birth 
      ? new Date(profile.date_of_birth).toLocaleDateString()
      : 'Not specified',
    completionPercentage: calculateProfileCompletion(profile),
    joinedDate: new Date(profile.created_at).toLocaleDateString(),
  };
}
