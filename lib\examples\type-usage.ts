// Example file showing how to use the types in your workout app
// This file demonstrates common patterns and can be deleted once you're familiar with the types

import { 
  // Database types
  Workout, 
  WorkoutInsert, 
  Session, 
  Profile,
  
  // API types
  CreateWorkoutRequest, 
  ApiResponse, 
  WorkoutWithSessionsResponse,
  
  // Domain types
  MuscleGroup, 
  Equipment, 
  WorkoutDifficulty,
  
  // Auth types
  UserWithProfile, 
  LoginFormData,
  
  // Utility types
  FormState,
  AsyncState
} from '@/lib/types';

// Example 1: Creating a new workout
export async function createWorkout(userId: string, workoutData: CreateWorkoutRequest): Promise<ApiResponse<Workout>> {
  const workoutInsert: WorkoutInsert = {
    name: workoutData.name,
    user_id: userId,
    description: workoutData.description,
    muscle_group: workoutData.muscle_group,
    equipment: workoutData.equipment,
    is_public: workoutData.is_public ?? false
  };

  try {
    // Your Supabase insert logic here
    // const { data, error } = await supabase.from('workouts').insert(workoutInsert).select().single();
    
    // Mock response for example
    const mockWorkout: Workout = {
      id: 'workout-123',
      ...workoutInsert,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return {
      data: mockWorkout,
      error: null,
      success: true
    };
  } catch (error) {
    return {
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    };
  }
}

// Example 2: Using enums for validation
export function validateWorkoutData(data: CreateWorkoutRequest): string[] {
  const errors: string[] = [];

  if (!data.name || data.name.trim().length === 0) {
    errors.push('Workout name is required');
  }

  if (data.muscle_group && !Object.values(MuscleGroup).includes(data.muscle_group as MuscleGroup)) {
    errors.push('Invalid muscle group');
  }

  if (data.equipment && !Object.values(Equipment).includes(data.equipment as Equipment)) {
    errors.push('Invalid equipment type');
  }

  return errors;
}

// Example 3: Form state management
export function useWorkoutForm() {
  const [formState, setFormState] = React.useState<FormState<CreateWorkoutRequest>>({
    data: {
      name: '',
      muscle_group: MuscleGroup.CHEST,
      equipment: Equipment.NONE,
      is_public: false
    },
    errors: {},
    isSubmitting: false,
    isValid: false,
    isDirty: false
  });

  const updateField = <K extends keyof CreateWorkoutRequest>(
    field: K, 
    value: CreateWorkoutRequest[K]
  ) => {
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, [field]: value },
      isDirty: true
    }));
  };

  const validateForm = () => {
    const errors = validateWorkoutData(formState.data);
    const errorMap: Partial<Record<keyof CreateWorkoutRequest, string>> = {};
    
    if (errors.length > 0) {
      errorMap.name = errors[0]; // Simplified error mapping
    }

    setFormState(prev => ({
      ...prev,
      errors: errorMap,
      isValid: errors.length === 0
    }));

    return errors.length === 0;
  };

  return { formState, updateField, validateForm };
}

// Example 4: API service with proper typing
export class WorkoutService {
  static async getWorkoutWithSessions(workoutId: string): Promise<AsyncState<WorkoutWithSessionsResponse>> {
    try {
      // Your API call here
      // const response = await fetch(`/api/workouts/${workoutId}/sessions`);
      // const data = await response.json();

      // Mock response for example
      const mockData: WorkoutWithSessionsResponse = {
        id: workoutId,
        name: 'Push-ups',
        user_id: 'user-123',
        is_public: true,
        description: 'Basic push-up exercise',
        muscle_group: MuscleGroup.CHEST,
        equipment: Equipment.NONE,
        image_url: null,
        video_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        sessions: [],
        totalSessions: 0
      };

      return {
        data: mockData,
        isLoading: false,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  static async getUserWorkouts(userId: string): Promise<ApiResponse<Workout[]>> {
    try {
      // Your Supabase query here
      // const { data, error } = await supabase
      //   .from('workouts')
      //   .select('*')
      //   .eq('user_id', userId);

      return {
        data: [], // Mock empty array
        error: null,
        success: true
      };
    } catch (error) {
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }
}

// Example 5: Type guards for runtime validation
export function isValidWorkout(obj: any): obj is Workout {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.user_id === 'string' &&
    typeof obj.is_public === 'boolean' &&
    typeof obj.created_at === 'string' &&
    typeof obj.updated_at === 'string'
  );
}

export function isValidSession(obj: any): obj is Session {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.workout_id === 'string' &&
    typeof obj.user_id === 'string' &&
    typeof obj.session_date === 'string' &&
    typeof obj.created_at === 'string'
  );
}

// Example 6: Working with user profiles
export function getProfileCompletionPercentage(profile: Profile): number {
  let completedFields = 0;
  const totalFields = 4; // profile_name, email, height_cm, date_of_birth

  if (profile.profile_name) completedFields++;
  if (profile.email) completedFields++;
  if (profile.height_cm) completedFields++;
  if (profile.date_of_birth) completedFields++;

  return Math.round((completedFields / totalFields) * 100);
}

// Example 7: Filtering and sorting
export function filterWorkoutsByMuscleGroup(workouts: Workout[], muscleGroup: MuscleGroup): Workout[] {
  return workouts.filter(workout => workout.muscle_group === muscleGroup);
}

export function sortWorkoutsByName(workouts: Workout[], order: 'asc' | 'desc' = 'asc'): Workout[] {
  return [...workouts].sort((a, b) => {
    const comparison = a.name.localeCompare(b.name);
    return order === 'asc' ? comparison : -comparison;
  });
}

// Example 8: Authentication helpers
export function createUserWithProfile(user: any, profile: Profile | null): UserWithProfile {
  return {
    ...user,
    profile: profile || undefined
  };
}

export function isUserAuthenticated(authState: { user: UserWithProfile | null }): boolean {
  return authState.user !== null;
}

// Note: This file is for demonstration purposes only.
// You can delete it once you're comfortable using the types in your actual components and services.
