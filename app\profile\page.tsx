import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { getCurrentUserProfile, formatProfileForDisplay } from "@/lib/utils/profile";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default async function Profile() {
  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) {
    redirect("/auth/login");
  }

  // Get user profile
  const profile = await getCurrentUserProfile();
  if (!profile) {
    redirect("/onboard");
  }

  const displayProfile = formatProfileForDisplay(profile);

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Your Profile</CardTitle>
          <CardDescription>
            Profile completion: {displayProfile.completionPercentage}%
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Name</h3>
              <p>{displayProfile.name}</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Email</h3>
              <p>{displayProfile.email}</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Height</h3>
              <p>{displayProfile.height}</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Date of Birth</h3>
              <p>{displayProfile.dateOfBirth}</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-sm text-muted-foreground">Member Since</h3>
              <p>{displayProfile.joinedDate}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
