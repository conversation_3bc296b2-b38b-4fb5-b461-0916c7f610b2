// Database table types based on supabasesql.md schema

export interface Profile {
  id: string; // UUID
  profile_name: string | null;
  email: string | null;
  height_cm: number | null;
  date_of_birth: string | null; // Date as ISO string
  created_at: string; // Timestamp as ISO string
  updated_at: string; // Timestamp as ISO string
}

export interface Workout {
  id: string; // UUID
  name: string;
  image_url: string | null;
  video_url: string | null;
  user_id: string; // UUID
  is_public: boolean;
  description: string | null;
  muscle_group: string | null;
  equipment: string | null;
  created_at: string; // Timestamp as ISO string
  updated_at: string; // Timestamp as ISO string
}

export interface Group {
  id: string; // UUID
  name: string;
  user_id: string; // UUID
  description: string | null;
  created_at: string; // Timestamp as ISO string
  updated_at: string; // Timestamp as ISO string
}

export interface GroupWorkout {
  id: string; // UUID
  group_id: string; // UUID
  workout_id: string; // UUID
  order_index: number;
  created_at: string; // Timestamp as ISO string
}

export interface Session {
  id: string; // UUID
  workout_id: string; // UUID
  user_id: string; // UUID
  set_count: number | null;
  rep_count: number | null;
  weight_kg: number | null;
  time_seconds: number | null;
  notes: string | null;
  session_date: string; // Date as ISO string
  created_at: string; // Timestamp as ISO string
}

// Insert types (for creating new records - without auto-generated fields)
export interface ProfileInsert {
  id: string; // Required for profiles as it references auth.users
  profile_name?: string | null;
  email?: string | null;
  height_cm?: number | null;
  date_of_birth?: string | null;
}

export interface WorkoutInsert {
  name: string;
  image_url?: string | null;
  video_url?: string | null;
  user_id: string;
  is_public?: boolean;
  description?: string | null;
  muscle_group?: string | null;
  equipment?: string | null;
}

export interface GroupInsert {
  name: string;
  user_id: string;
  description?: string | null;
}

export interface GroupWorkoutInsert {
  group_id: string;
  workout_id: string;
  order_index?: number;
}

export interface SessionInsert {
  workout_id: string;
  user_id: string;
  set_count?: number | null;
  rep_count?: number | null;
  weight_kg?: number | null;
  time_seconds?: number | null;
  notes?: string | null;
  session_date?: string;
}

// Update types (for updating existing records - all fields optional except id)
export interface ProfileUpdate {
  profile_name?: string | null;
  email?: string | null;
  height_cm?: number | null;
  date_of_birth?: string | null;
}

export interface WorkoutUpdate {
  name?: string;
  image_url?: string | null;
  video_url?: string | null;
  is_public?: boolean;
  description?: string | null;
  muscle_group?: string | null;
  equipment?: string | null;
}

export interface GroupUpdate {
  name?: string;
  description?: string | null;
}

export interface SessionUpdate {
  set_count?: number | null;
  rep_count?: number | null;
  weight_kg?: number | null;
  time_seconds?: number | null;
  notes?: string | null;
  session_date?: string;
}
