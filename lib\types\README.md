# Types Documentation

This directory contains all TypeScript type definitions for the workout app. The types are organized by domain and functionality.

## File Structure

```
lib/types/
├── index.ts          # Central export file - import all types from here
├── database.ts       # Database table interfaces and CRUD types
├── api.ts           # API request/response DTOs
├── workout.ts       # Workout domain-specific types and enums
├── auth.ts          # Authentication and user-related types
├── supabase-utils.ts # Utilities for converting between Supabase and API responses
└── README.md        # This documentation file
```

## Usage

### Importing Types

Always import types from the main index file:

```typescript
import { 
  Workout, 
  CreateWorkoutRequest, 
  MuscleGroup, 
  UserWithProfile 
} from '@/lib/types';
```

### Database Types

Use these types when working with Supabase database operations:

```typescript
import {
  Workout,
  WorkoutInsert,
  WorkoutUpdate,
  SupabaseResponse,
  toApiResponse
} from '@/lib/types';

// Creating a new workout
const newWorkout: WorkoutInsert = {
  name: "Push-ups",
  user_id: "user-123",
  muscle_group: "Chest",
  equipment: "None"
};

// Direct Supabase call
const response: SupabaseResponse<Workout> = await supabase
  .from('workouts')
  .insert(newWorkout)
  .select()
  .single();

// Convert to API response
const apiResponse = toApiResponse(response);
```

### API Types

Use these for API endpoints and data transfer:

```typescript
import { CreateWorkoutRequest, ApiResponse, WorkoutResponse } from '@/lib/types';

// API request
const request: CreateWorkoutRequest = {
  name: "Squats",
  muscle_group: "Legs",
  equipment: "None"
};

// API response
const response: ApiResponse<WorkoutResponse> = {
  data: workout,
  error: null,
  success: true
};
```

### Workout Domain Types

Use enums and domain types for business logic:

```typescript
import { MuscleGroup, Equipment, WorkoutDifficulty } from '@/lib/types';

// Using enums
const muscleGroup = MuscleGroup.CHEST;
const equipment = Equipment.DUMBBELLS;
const difficulty = WorkoutDifficulty.INTERMEDIATE;

// Form validation
const isValidMuscleGroup = Object.values(MuscleGroup).includes(userInput);
```

### Authentication Types

Use these for user management and auth flows:

```typescript
import { UserWithProfile, LoginFormData, AuthState } from '@/lib/types';

// Login form
const loginData: LoginFormData = {
  email: "<EMAIL>",
  password: "password123"
};

// Auth state management
const authState: AuthState = {
  user: null,
  loading: false,
  error: null,
  isAuthenticated: false
};
```

## Type Categories

### 1. Database Types (`database.ts`)
- **Base interfaces**: `Profile`, `Workout`, `Group`, `Session`, etc.
- **Insert types**: For creating new records (excludes auto-generated fields)
- **Update types**: For updating existing records (all fields optional)
- **Supabase types**: `SupabaseResponse<T>`, `PostgrestError`, etc.

### 2. API Types (`api.ts`)
- **Request DTOs**: Data structures for API requests
- **Response DTOs**: Data structures for API responses
- **Pagination**: Common pagination patterns
- **Search/Filter**: Query parameter types

### 3. Workout Domain Types (`workout.ts`)
- **Enums**: `MuscleGroup`, `Equipment`, `WorkoutDifficulty`, etc.
- **Business logic types**: `WorkoutTemplate`, `ExerciseSet`, `WorkoutPlan`
- **Analytics**: Progress tracking and statistics
- **UI state**: Form data and filter configurations

### 4. Auth Types (`auth.ts`)
- **User management**: Extended user types with profile data
- **Form types**: Login, signup, password reset forms
- **Session management**: Authentication state and tokens
- **Permissions**: User roles and access control

### 5. Supabase Utilities (`supabase-utils.ts`)
- **Response converters**: `toApiResponse()`, `toPaginatedResponse()`
- **Error handling**: `formatSupabaseError()`, `handleSupabaseResponse()`
- **Safe operations**: `safeSupabaseOperation()`, `unwrapSupabaseResponse()`
- **Type guards**: `hasSupabaseError()`, `hasSupabaseData()`

## When to Use Each Pattern

### Use Supabase Types For:
- **Direct database operations** in server components
- **Internal service methods** that don't need API formatting
- **Repository pattern** implementations
- **When you need the raw Supabase error details**

```typescript
// ✅ Good for server-side database operations
const { data: workout, error } = await supabase
  .from('workouts')
  .select('*')
  .eq('id', workoutId)
  .single();
```

### Use API Types For:
- **API route handlers** (Next.js API routes)
- **Frontend-backend communication**
- **Consistent error messaging** across your app
- **When you need standardized success/error responses**

```typescript
// ✅ Good for API endpoints
export async function GET(): Promise<Response> {
  const response: ApiResponse<Workout> = {
    data: workout,
    error: null,
    success: true
  };
  return Response.json(response);
}
```

### Use Utility Functions For:
- **Converting between patterns** when needed
- **Consistent error handling** across your app
- **Safe operations** that won't throw unexpected errors

```typescript
// ✅ Convert Supabase response to API response
const apiResponse = toApiResponse(supabaseResponse);

// ✅ Safe operation with automatic error handling
const result = await safeSupabaseOperation(
  () => supabase.from('workouts').select('*'),
  'Failed to fetch workouts'
);
```

## Best Practices

### 1. Type Safety
```typescript
// ✅ Good - Use specific types
const workout: Workout = { /* ... */ };

// ❌ Bad - Avoid any
const workout: any = { /* ... */ };
```

### 2. Enum Usage
```typescript
// ✅ Good - Use enums for constants
const muscleGroup = MuscleGroup.CHEST;

// ❌ Bad - Magic strings
const muscleGroup = "Chest";
```

### 3. Optional vs Required
```typescript
// ✅ Good - Clear about what's required
interface CreateWorkoutRequest {
  name: string;           // Required
  description?: string;   // Optional
}
```

### 4. Generic Types
```typescript
// ✅ Good - Reusable generic patterns
const response: ApiResponse<Workout[]> = { /* ... */ };
const state: AsyncState<Profile> = { /* ... */ };
```

### 5. Type Guards
```typescript
// ✅ Good - Type guards for runtime checks
function isWorkout(obj: any): obj is Workout {
  return obj && typeof obj.id === 'string' && typeof obj.name === 'string';
}
```

## Common Patterns

### Form Handling
```typescript
import { FormState, CreateWorkoutRequest } from '@/lib/types';

const [formState, setFormState] = useState<FormState<CreateWorkoutRequest>>({
  data: { name: '', muscle_group: MuscleGroup.CHEST },
  errors: {},
  isSubmitting: false,
  isValid: false,
  isDirty: false
});
```

### API Calls
```typescript
import { ApiResponse, WorkoutResponse } from '@/lib/types';

async function fetchWorkout(id: string): Promise<ApiResponse<WorkoutResponse>> {
  // Implementation
}
```

### Database Operations
```typescript
import { DatabaseResult, Workout } from '@/lib/types';

async function getWorkout(id: string): Promise<DatabaseResult<Workout>> {
  // Implementation
}
```

## Extending Types

When you need to extend existing types:

```typescript
// Extend database types for specific use cases
interface WorkoutWithStats extends Workout {
  totalSessions: number;
  averageRating: number;
}

// Create union types for specific scenarios
type WorkoutStatus = 'draft' | 'published' | 'archived';

// Use utility types for transformations
type PartialWorkout = Partial<Workout>;
type RequiredWorkoutFields = Required<Pick<Workout, 'name' | 'user_id'>>;
```

## Migration Guide

If you're updating existing code to use these types:

1. Replace `any` types with specific interfaces
2. Use enums instead of string literals
3. Add proper error handling with `ApiResponse` wrapper
4. Implement form validation with `FormState`
5. Use database operation types for Supabase calls
