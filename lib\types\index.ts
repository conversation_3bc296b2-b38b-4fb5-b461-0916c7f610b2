// Central export file for all types

// Database types
export type {
  Profile,
  Workout,
  Group,
  GroupWorkout,
  Session,
  ProfileInsert,
  WorkoutInsert,
  GroupInsert,
  GroupWorkoutInsert,
  SessionInsert,
  ProfileUpdate,
  WorkoutUpdate,
  GroupUpdate,
  SessionUpdate,
  SupabaseResponse,
  SupabaseListResponse,
  PostgrestError,
  DatabaseResult,
  DatabaseListResult,
} from './database';

// API types
export type {
  ApiResponse,
  PaginationParams,
  PaginatedResponse,
  CreateProfileRequest,
  UpdateProfileRequest,
  ProfileResponse,
  CreateWorkoutRequest,
  UpdateWorkoutRequest,
  WorkoutResponse,
  WorkoutWithSessionsResponse,
  CreateGroupRequest,
  UpdateGroupRequest,
  AddWorkoutToGroupRequest,
  GroupResponse,
  GroupWithWorkoutsResponse,
  CreateSessionRequest,
  UpdateSessionRequest,
  SessionResponse,
  SessionWithWorkoutResponse,
  WorkoutSearchParams,
  SessionFilterParams,
  WorkoutStatsResponse,
  UserStatsResponse,
  ValidationError,
  ApiError,
} from './api';

// Workout domain types
export {
  MuscleGroup,
  Equipment,
  WorkoutDifficulty,
  SessionType,
  MeasurementType,
  AnalyticsPeriod,
} from './workout';

export type {
  WorkoutTemplate,
  ExerciseSet,
  WorkoutPlan,
  WorkoutSchedule,
  ProgressEntry,
  BodyMeasurement,
  ActiveWorkoutSession,
  WorkoutAnalytics,
  WorkoutFormData,
  SessionFormData,
  WorkoutListFilters,
  SessionListFilters,
} from './workout';

// Auth types
export {
  UserRole,
} from './auth';

export type {
  UserWithProfile,
  LoginFormData,
  SignUpFormData,
  ForgotPasswordFormData,
  ResetPasswordFormData,
  UpdatePasswordFormData,
  OnboardingFormData,
  AuthState,
  SessionInfo,
  UserPreferences,
  OAuthProvider,
  OAuthSignInOptions,
  AccountSettings,
  ProfileCompletionStatus,
  UserPermissions,
  UserConnection,
  FriendRequest,
  Subscription,
  SubscriptionPlan,
  AuthError,
  AuthResponse,
  ProfileUpdateResponse,
} from './auth';

// Common utility types
export type ID = string;
export type Timestamp = string;
export type DateString = string;

// Supabase utilities
export {
  toApiResponse,
  toPaginatedResponse,
  formatSupabaseError,
  hasSupabaseError,
  hasSupabaseData,
  handleSupabaseResponse,
  safeSupabaseOperation,
  safeSupabaseListOperation,
  createErrorResponse,
  createSuccessResponse,
  unwrapSupabaseResponse,
  unwrapSupabaseListResponse,
} from './supabase-utils';

// Generic types for common patterns
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Form state types
export interface FormState<T> {
  data: T;
  errors: Partial<Record<keyof T, string>>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface AsyncState<T> extends LoadingState {
  data: T | null;
}

// Sort and filter types
export type SortOrder = 'asc' | 'desc';

export interface SortConfig<T> {
  field: keyof T;
  order: SortOrder;
}

export interface FilterConfig<T> {
  field: keyof T;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in' | 'not_in';
  value: any;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface DataTableProps<T> extends BaseComponentProps {
  data: T[];
  columns: Array<{
    key: keyof T;
    label: string;
    sortable?: boolean;
    render?: (value: T[keyof T], row: T) => React.ReactNode;
  }>;
  loading?: boolean;
  error?: string | null;
  onSort?: (field: keyof T, order: SortOrder) => void;
  onFilter?: (filters: FilterConfig<T>[]) => void;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    onPageChange: (page: number) => void;
  };
}
