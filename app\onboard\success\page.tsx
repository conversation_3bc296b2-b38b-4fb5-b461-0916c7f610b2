import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function OnboardingSuccess() {
  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              Welcome to Your Fitness Journey! 🎉
            </CardTitle>
            <CardDescription className="text-center">
              Your profile has been set up successfully
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground text-center">
              You're all set! You can now start tracking your workouts, 
              creating exercise routines, and monitoring your progress.
            </p>
            
            <div className="space-y-2">
              <Button asChild className="w-full">
                <Link href="/protected">
                  Get Started
                </Link>
              </Button>
              
              <Button asChild variant="outline" className="w-full">
                <Link href="/profile">
                  View Profile
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
